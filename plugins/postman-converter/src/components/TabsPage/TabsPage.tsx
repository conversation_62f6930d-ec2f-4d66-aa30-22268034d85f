import { useState } from 'react';
import { Content, ContentHeader, SupportButton } from '@backstage/core-components';
import {
  Tabs,
  Tab,
  Box,
  makeStyles,
  Paper,
  Divider,
  useTheme
} from '@material-ui/core';
import CollectionIcon from '@material-ui/icons/CollectionsBookmark';
import K6Icon from '@material-ui/icons/Speed';
import TestingIcon from '@material-ui/icons/BugReport';
import { CollectionListPage } from '../CollectionListPage';
import { K6ConverterPage } from '../K6ConverterPage';
import { ApiTestingPage } from '../ApiTestingPage';

const useStyles = makeStyles(theme => ({
  tabContent: {
    marginTop: theme.spacing(2),
  },
  tabsContainer: {
    borderRadius: theme.shape.borderRadius,
    backgroundColor: theme.palette.background.paper,
    boxShadow: theme.shadows[1],
    overflow: 'hidden',
  },
  tabs: {
    minHeight: 64,
    '& .MuiTab-root': {
      minHeight: 64,
      textTransform: 'none',
      fontWeight: theme.typography.fontWeightMedium,
      fontSize: '0.95rem',
      padding: theme.spacing(2, 3),
      transition: 'all 0.2s',
      '&:hover': {
        backgroundColor: theme.palette.action.hover,
      },
    },
  },
  tabPanel: {
    padding: theme.spacing(3),
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.shape.borderRadius,
    boxShadow: theme.shadows[1],
  },
  tabIcon: {
    marginRight: theme.spacing(1),
    verticalAlign: 'middle',
  },
}));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  const classes = useStyles();

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`postman-converter-tabpanel-${index}`}
      aria-labelledby={`postman-converter-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box className={classes.tabPanel}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `postman-converter-tab-${index}`,
    'aria-controls': `postman-converter-tabpanel-${index}`,
  };
}

export const TabsPage = () => {
  const classes = useStyles();
  const theme = useTheme();
  const [value, setValue] = useState(0);

  const handleChange = (_event: React.ChangeEvent<{}>, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Content>
      <ContentHeader title="Postman Converter">
        <SupportButton>
          Manage Postman collections, convert to K6 scripts, and run API tests
        </SupportButton>
      </ContentHeader>

      <Paper className={classes.tabsContainer} elevation={0}>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="Postman converter tabs"
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
          className={classes.tabs}
        >
          <Tab
            label={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <CollectionIcon
                  className={classes.tabIcon}
                  style={{ color: value === 0 ? theme.palette.primary.main : 'inherit' }}
                />
                Collection Management
              </div>
            }
            {...a11yProps(0)}
          />
          <Tab
            label={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <K6Icon
                  className={classes.tabIcon}
                  style={{ color: value === 1 ? theme.palette.primary.main : 'inherit' }}
                />
                <span>K6 Converter</span>
              </div>
            }
            {...a11yProps(1)}
          />
          <Tab
            label={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <TestingIcon
                  className={classes.tabIcon}
                  style={{ color: value === 2 ? theme.palette.primary.main : 'inherit' }}
                />
                <span>API Testing</span>
              </div>
            }
            {...a11yProps(2)}
          />
        </Tabs>
        <Divider />
      </Paper>

      <div className={classes.tabContent}>
        <TabPanel value={value} index={0}>
          <CollectionListPage />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <K6ConverterPage />
        </TabPanel>
        <TabPanel value={value} index={2}>
          <ApiTestingPage />
        </TabPanel>
      </div>
    </Content>
  );
};
