import { useState, useCallback } from 'react';

export const useUnsavedChanges = () => {
  const [unsavedCollections, setUnsavedCollections] = useState<Set<string>>(new Set());
  const [isSaving, setIsSaving] = useState<Record<string, boolean>>({});

  // Helper function to mark a collection as having unsaved changes
  const markCollectionAsUnsaved = useCallback((collectionId: string) => {
    setUnsavedCollections(prev => new Set([...prev, collectionId]));
  }, []);

  // Helper function to mark a collection as saved
  const markCollectionAsSaved = useCallback((collectionId: string) => {
    setUnsavedCollections(prev => {
      const newSet = new Set(prev);
      newSet.delete(collectionId);
      return newSet;
    });
  }, []);

  // Helper function to set saving state for a collection
  const setSavingState = useCallback((collectionId: string, saving: boolean) => {
    setIsSaving(prev => ({ ...prev, [collectionId]: saving }));
  }, []);

  // Helper function to check if a collection has unsaved changes
  const hasUnsavedChanges = useCallback((collectionId: string) => {
    return unsavedCollections.has(collectionId);
  }, [unsavedCollections]);

  // Helper function to check if a collection is currently being saved
  const isSavingCollection = useCallback((collectionId: string) => {
    return isSaving[collectionId] || false;
  }, [isSaving]);

  // Helper function to get all collections with unsaved changes
  const getUnsavedCollections = useCallback(() => {
    return Array.from(unsavedCollections);
  }, [unsavedCollections]);

  // Helper function to check if any collection is currently being saved
  const isAnySaving = useCallback(() => {
    return Object.values(isSaving).some(saving => saving);
  }, [isSaving]);

  return {
    unsavedCollections,
    isSaving,
    markCollectionAsUnsaved,
    markCollectionAsSaved,
    setSavingState,
    hasUnsavedChanges,
    isSavingCollection,
    getUnsavedCollections,
    isAnySaving,
  };
};
