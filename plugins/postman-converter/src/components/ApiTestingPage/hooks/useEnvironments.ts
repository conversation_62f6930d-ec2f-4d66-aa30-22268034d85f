import { useState, useCallback } from 'react';
import { ApiEnvironment } from '../../../types';
import { createNewEnvironment } from '../utils/requestUtils';

export const useEnvironments = () => {
  // State for environments
  const [environments, setEnvironments] = useState<ApiEnvironment[]>([]);
  const [currentEnvironment, setCurrentEnvironment] = useState<string>('');
  const [isEnvironmentDialogOpen, setIsEnvironmentDialogOpen] = useState<boolean>(false);
  const [isImportEnvironmentDialogOpen, setIsImportEnvironmentDialogOpen] = useState<boolean>(false);
  const [environmentToEdit, setEnvironmentToEdit] = useState<ApiEnvironment | null>(null);

  // Handle add environment
  const handleAddEnvironment = useCallback(() => {
    const newEnvironment = createNewEnvironment();
    setEnvironmentToEdit(newEnvironment);
    setIsEnvironmentDialogOpen(true);
  }, []);

  // Handle edit environment
  const handleEditEnvironment = useCallback((environmentId: string) => {
    const environment = environments.find(env => env.id === environmentId);
    if (environment) {
      setEnvironmentToEdit(environment);
      setIsEnvironmentDialogOpen(true);
    }
  }, [environments]);

  // Handle save environment
  const handleSaveEnvironment = useCallback((environment: ApiEnvironment) => {
    // Check if this is a new environment or an existing one
    const existingIndex = environments.findIndex(env => env.id === environment.id);

    if (existingIndex >= 0) {
      // Update existing environment
      const updatedEnvironments = [...environments];
      updatedEnvironments[existingIndex] = environment;
      setEnvironments(updatedEnvironments);
    } else {
      // Add new environment
      setEnvironments([...environments, environment]);
    }

    // Close the dialog
    setIsEnvironmentDialogOpen(false);
    setEnvironmentToEdit(null);

    return { success: true, environment };
  }, [environments]);

  // Handle delete environment
  const handleDeleteEnvironment = useCallback((environmentId: string) => {
    // Remove from environments state
    setEnvironments(prevEnvironments =>
      prevEnvironments.filter(env => env.id !== environmentId)
    );

    // If this was the current environment, reset it
    if (currentEnvironment === environmentId) {
      setCurrentEnvironment('');
    }

    return { success: true };
  }, [currentEnvironment]);

  // Handle import environment
  const handleImportEnvironment = useCallback((environment: ApiEnvironment) => {
    setEnvironments(prev => [...prev, environment]);
    return { success: true, environment };
  }, []);

  // Handle environment change
  const handleEnvironmentChange = useCallback((environmentId: string) => {
    setCurrentEnvironment(environmentId);
  }, []);

  // Get selected environment
  const selectedEnvironment = environments.find(env => env.id === currentEnvironment);

  return {
    environments,
    setEnvironments,
    currentEnvironment,
    setCurrentEnvironment,
    selectedEnvironment,
    isEnvironmentDialogOpen,
    setIsEnvironmentDialogOpen,
    isImportEnvironmentDialogOpen,
    setIsImportEnvironmentDialogOpen,
    environmentToEdit,
    setEnvironmentToEdit,
    handleAddEnvironment,
    handleEditEnvironment,
    handleSaveEnvironment,
    handleDeleteEnvironment,
    handleImportEnvironment,
    handleEnvironmentChange,
  };
};
