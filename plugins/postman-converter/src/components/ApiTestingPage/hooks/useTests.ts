import { useState, useCallback } from 'react';
import { ApiResponse, ApiRequest } from '../../../types';
import { TestResult } from '../TestResultsPanel';
import { generateTestScript } from '../../../utils/testGenerator';
import { executeTests } from '../../../utils/testExecutor';

export const useTests = () => {
  const [isGeneratingTests, setIsGeneratingTests] = useState(false);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testError, setTestError] = useState<string | null>(null);

  // Handle test generation
  const handleGenerateTests = useCallback((response: ApiResponse) => {
    setIsGeneratingTests(true);
    setTestError(null);

    try {
      // Parse the response body as JSON if possible
      let responseData = null;
      try {
        if (response.body &&
            response.headers &&
            response.headers['content-type'] &&
            response.headers['content-type'].includes('application/json')) {
          responseData = JSON.parse(response.body);
        }
      } catch (error) {
        // Silently handle JSON parsing errors
        responseData = null;
      }

      // Generate test script using the utility function
      const testScript = generateTestScript({
        status: response.status,
        headers: response.headers,
        data: responseData
      });

      return testScript;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setTestError(errorMessage);
      throw error;
    } finally {
      setIsGeneratingTests(false);
    }
  }, []);

  // Handle running tests
  const handleRunTests = useCallback(async (response: ApiResponse, testScript: string) => {
    if (!response || !testScript) return [];

    setIsRunningTests(true);
    setTestError(null);
    setTestResults([]);

    try {
      // Use the real test executor to run the tests
      const executorResults = executeTests(response, testScript);

      // Convert executor results to the format expected by the UI
      const results: TestResult[] = executorResults.map(result => ({
        id: result.id || `test-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        name: result.name,
        passed: result.passed,
        error: result.error,
        duration: result.duration || 0
      }));

      setTestResults(results);
      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setTestError(errorMessage);
      throw error;
    } finally {
      setIsRunningTests(false);
    }
  }, []);

  // Clear test results
  const clearTestResults = useCallback(() => {
    setTestResults([]);
    setTestError(null);
  }, []);

  // Clear test error
  const clearTestError = useCallback(() => {
    setTestError(null);
  }, []);

  return {
    isGeneratingTests,
    isRunningTests,
    testResults,
    testError,
    handleGenerateTests,
    handleRunTests,
    clearTestResults,
    clearTestError,
    setTestResults,
  };
};
