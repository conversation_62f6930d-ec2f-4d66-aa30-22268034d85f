import { useState, useCallback } from 'react';

export interface RenameDialogData {
  itemType: 'collection' | 'folder' | 'request';
  itemId: string;
  collectionId: string;
  currentName: string;
}

export const useDialogs = () => {
  // Import/Export dialogs
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  // Create dialogs
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [createRequestDialogOpen, setCreateRequestDialogOpen] = useState(false);

  // Rename dialog
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [renameDialogData, setRenameDialogData] = useState<RenameDialogData | null>(null);

  // Edit collection dialog
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editCollectionId, setEditCollectionId] = useState('');
  const [editCollectionName, setEditCollectionName] = useState('');
  const [editCollectionDescription, setEditCollectionDescription] = useState('');

  // Environment dialog
  const [environmentDialogOpen, setEnvironmentDialogOpen] = useState(false);

  // Selected items for actions
  const [selectedCollectionForAction, setSelectedCollectionForAction] = useState('');
  const [selectedFolderForAction, setSelectedFolderForAction] = useState('');

  // Import/Export handlers
  const openImportDialog = useCallback(() => setImportDialogOpen(true), []);
  const closeImportDialog = useCallback(() => setImportDialogOpen(false), []);
  const openExportDialog = useCallback(() => setExportDialogOpen(true), []);
  const closeExportDialog = useCallback(() => setExportDialogOpen(false), []);

  // Create dialog handlers
  const openCreateFolderDialog = useCallback((collectionId: string, parentFolderId?: string) => {
    setSelectedCollectionForAction(collectionId);
    setSelectedFolderForAction(parentFolderId || '');
    setCreateFolderDialogOpen(true);
  }, []);

  const closeCreateFolderDialog = useCallback(() => {
    setCreateFolderDialogOpen(false);
    setSelectedCollectionForAction('');
    setSelectedFolderForAction('');
  }, []);

  const openCreateRequestDialog = useCallback((collectionId: string, folderId?: string) => {
    setSelectedCollectionForAction(collectionId);
    setSelectedFolderForAction(folderId || '');
    setCreateRequestDialogOpen(true);
  }, []);

  const closeCreateRequestDialog = useCallback(() => {
    setCreateRequestDialogOpen(false);
    setSelectedCollectionForAction('');
    setSelectedFolderForAction('');
  }, []);

  // Rename dialog handlers
  const openRenameDialog = useCallback((data: RenameDialogData) => {
    setRenameDialogData(data);
    setRenameDialogOpen(true);
  }, []);

  const closeRenameDialog = useCallback(() => {
    setRenameDialogOpen(false);
    setRenameDialogData(null);
  }, []);

  // Edit collection dialog handlers
  const openEditDialog = useCallback((collectionId: string, name: string, description: string) => {
    setEditCollectionId(collectionId);
    setEditCollectionName(name);
    setEditCollectionDescription(description);
    setEditDialogOpen(true);
  }, []);

  const closeEditDialog = useCallback(() => {
    setEditDialogOpen(false);
    setEditCollectionId('');
    setEditCollectionName('');
    setEditCollectionDescription('');
  }, []);

  // Environment dialog handlers
  const openEnvironmentDialog = useCallback(() => setEnvironmentDialogOpen(true), []);
  const closeEnvironmentDialog = useCallback(() => setEnvironmentDialogOpen(false), []);

  return {
    // Import/Export
    importDialogOpen,
    exportDialogOpen,
    openImportDialog,
    closeImportDialog,
    openExportDialog,
    closeExportDialog,

    // Create dialogs
    createFolderDialogOpen,
    createRequestDialogOpen,
    selectedCollectionForAction,
    selectedFolderForAction,
    openCreateFolderDialog,
    closeCreateFolderDialog,
    openCreateRequestDialog,
    closeCreateRequestDialog,

    // Rename dialog
    renameDialogOpen,
    renameDialogData,
    openRenameDialog,
    closeRenameDialog,

    // Edit collection dialog
    editDialogOpen,
    editCollectionId,
    editCollectionName,
    editCollectionDescription,
    setEditCollectionName,
    setEditCollectionDescription,
    openEditDialog,
    closeEditDialog,

    // Environment dialog
    environmentDialogOpen,
    openEnvironmentDialog,
    closeEnvironmentDialog,
  };
};
