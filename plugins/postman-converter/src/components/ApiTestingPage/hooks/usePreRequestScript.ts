import { useState, useCallback } from 'react';

export const usePreRequestScript = () => {
  const [isSavingPreRequestScript, setIsSavingPreRequestScript] = useState(false);
  const [preRequestScriptError, setPreRequestScriptError] = useState<string | null>(null);

  // <PERSON>le saving pre-request scripts
  const handleSavePreRequestScript = useCallback(async (script: string) => {
    setIsSavingPreRequestScript(true);
    setPreRequestScriptError(null);

    try {
      // Validate the script (basic validation)
      if (script.trim() && !script.includes('function') && !script.includes('pm.')) {
        console.warn('Pre-request script may not be valid JavaScript or Postman script');
      }

      return script;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setPreRequestScriptError(errorMessage);
      throw error;
    } finally {
      setIsSavingPreRequestScript(false);
    }
  }, []);

  // Clear pre-request script error
  const clearPreRequestScriptError = useCallback(() => {
    setPreRequestScriptError(null);
  }, []);

  return {
    isSavingPreRequestScript,
    preRequestScriptError,
    handleSavePreRequestScript,
    clearPreRequestScriptError,
  };
};
