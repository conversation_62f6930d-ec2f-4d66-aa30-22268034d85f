import React from 'react';
import {
  Grid,
  Typography,
  makeStyles,
  Box,
  Snackbar,
} from '@material-ui/core';
import {
  InfoCard,
} from '@backstage/core-components';
import { Alert } from '@material-ui/lab';

// Components
import { CollectionsSidebar } from './components/CollectionsSidebar';
import { RequestPanel } from './components/RequestPanel';
import { ResponsePanel } from './components/ResponsePanel';
import { EnvironmentSelector } from './components/EnvironmentSelector';
import { ImportDialog } from './ImportDialog';
import { ExportDialog } from './ExportDialog';
import { CreateFolderDialog } from './CreateFolderDialog';
import { CreateRequestDialog } from './CreateRequestDialog';
import { RenameDialog } from './components/RenameDialog';

// Hooks
import { useCollections } from './hooks/useCollections';
import { useEnvironments } from './hooks/useEnvironments';
import { useRequest } from './hooks/useRequest';
import { useSnackbar } from './hooks/useSnackbar';
import { useDialogs } from './hooks/useDialogs';

// Icons
import TestingIcon from '@material-ui/icons/BugReport';

// Types
import { ApiCollection, ApiEnvironment, ApiRequest } from '../../types';

const useStyles = makeStyles(theme => ({
  root: {
    height: '100%',
  },
  infoCard: {
    marginBottom: theme.spacing(3),
  },
}));

export const ApiTestingPage = () => {
  const classes = useStyles();

  // Initialize hooks
  const collections = useCollections();
  const environments = useEnvironments();
  const request = useRequest(collections.collections, collections.setCollections);
  const snackbar = useSnackbar();
  const dialogs = useDialogs();

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <InfoCard
            title={
              <Box display="flex" alignItems="center">
                <TestingIcon style={{ marginRight: '8px' }} />
                API Testing
              </Box>
            }
            className={classes.infoCard}
          >
            <Typography variant="body1" paragraph>
              Test your APIs by sending requests and viewing responses. Organize your requests into collections and use environments to manage variables.
            </Typography>
          </InfoCard>
        </Grid>

        {/* Main content grid */}
        <Grid container item xs={12} spacing={3}>
          {/* Collections sidebar */}
          <Grid item xs={12} md={3}>
            <CollectionsSidebar
              collections={collections.collections}
              collectionsLoading={collections.collectionsLoading}
              collectionsError={collections.collectionsError}
              expandedFolders={collections.expandedFolders}
              selectedItemId={collections.selectedItemId}
              onFolderToggle={collections.handleFolderToggle}
              onItemSelect={collections.handleItemSelect}
              onAddCollection={collections.handleAddCollection}
              onDeleteCollection={collections.handleDeleteCollection}
              onEditCollection={(collectionId: string) => {
                const collection = collections.collections.find(c => c.id === collectionId);
                if (collection) {
                  dialogs.openEditDialog(collectionId, collection.name, collection.description);
                }
              }}
              onAddFolder={dialogs.openCreateFolderDialog}
              onAddRequest={dialogs.openCreateRequestDialog}
              onDeleteRequest={(requestId: string) => {
                // TODO: Implement delete request
              }}
              onDeleteFolder={(folderId: string) => {
                // TODO: Implement delete folder
              }}
              onDuplicateRequest={(requestId: string) => {
                // TODO: Implement duplicate request
              }}
              onRenameCollection={(collectionId: string, currentName: string) => {
                dialogs.openRenameDialog({
                  itemType: 'collection',
                  itemId: collectionId,
                  collectionId,
                  currentName,
                });
              }}
              onRenameFolder={(collectionId: string, folderId: string, currentName: string) => {
                dialogs.openRenameDialog({
                  itemType: 'folder',
                  itemId: folderId,
                  collectionId,
                  currentName,
                });
              }}
              onRenameRequest={(collectionId: string, requestId: string, currentName: string) => {
                dialogs.openRenameDialog({
                  itemType: 'request',
                  itemId: requestId,
                  collectionId,
                  currentName,
                });
              }}
            />
          </Grid>

          {/* Request and response area */}
          <Grid item xs={12} md={9}>
            <EnvironmentSelector
              environments={environments.environments}
              currentEnvironment={environments.currentEnvironment}
              onEnvironmentChange={environments.handleEnvironmentChange}
              onOpenEnvironmentDialog={dialogs.openEnvironmentDialog}
              onOpenExportDialog={dialogs.openExportDialog}
            />

            <RequestPanel
              currentRequest={request.currentRequest}
              onRequestChange={request.setCurrentRequest}
              onSendRequest={request.handleSendRequest}
              isLoading={request.isLoading}
              selectedEnvironment={environments.selectedEnvironment}
            />

            <ResponsePanel
              response={request.currentResponse}
              isLoading={request.isLoading}
            />
          </Grid>
        </Grid>
      </Grid>

      {/* Dialogs */}
      <ImportDialog
        open={dialogs.importDialogOpen}
        onClose={dialogs.closeImportDialog}
        onImportCollection={collections.handleImportCollection}
        onImportEnvironment={environments.handleImportEnvironment}
      />

      <ExportDialog
        open={dialogs.exportDialogOpen}
        onClose={dialogs.closeExportDialog}
        collections={collections.collections}
        environments={environments.environments}
      />

      <CreateFolderDialog
        open={dialogs.createFolderDialogOpen}
        onClose={dialogs.closeCreateFolderDialog}
        onCreateFolder={(folderName: string) => {
          collections.handleAddFolder(
            dialogs.selectedCollectionForAction,
            dialogs.selectedFolderForAction || undefined,
            folderName
          );
          dialogs.closeCreateFolderDialog();
        }}
        collections={collections.collections}
        selectedCollectionId={dialogs.selectedCollectionForAction}
        selectedFolderId={dialogs.selectedFolderForAction}
      />

      <CreateRequestDialog
        open={dialogs.createRequestDialogOpen}
        onClose={dialogs.closeCreateRequestDialog}
        onCreateRequest={(requestName: string, method: any, url: string) => {
          collections.handleAddRequest(
            dialogs.selectedCollectionForAction,
            dialogs.selectedFolderForAction || undefined,
            requestName,
            method,
            url
          );
          dialogs.closeCreateRequestDialog();
        }}
        collections={collections.collections}
        selectedCollectionId={dialogs.selectedCollectionForAction}
        selectedFolderId={dialogs.selectedFolderForAction}
      />

      <RenameDialog
        open={dialogs.renameDialogOpen}
        onClose={dialogs.closeRenameDialog}
        onRename={(newName: string) => {
          if (dialogs.renameDialogData) {
            const { itemType, itemId, collectionId } = dialogs.renameDialogData;

            if (itemType === 'collection') {
              collections.handleRenameCollection(collectionId, newName);
            } else if (itemType === 'folder') {
              collections.handleRenameFolder(collectionId, itemId, newName);
            } else if (itemType === 'request') {
              collections.handleRenameRequest(collectionId, itemId, newName);
            }
          }
          dialogs.closeRenameDialog();
        }}
        currentName={dialogs.renameDialogData?.currentName || ''}
        itemType={dialogs.renameDialogData?.itemType || 'collection'}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.snackbar.open}
        autoHideDuration={6000}
        onClose={snackbar.hideSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert onClose={snackbar.hideSnackbar} severity={snackbar.snackbar.severity}>
          {snackbar.snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};