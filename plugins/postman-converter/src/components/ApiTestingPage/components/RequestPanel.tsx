import React from 'react';
import {
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Button,
  Tabs,
  Tab,
  CircularProgress,
  makeStyles,
} from '@material-ui/core';
import SendIcon from '@material-ui/icons/Send';
import { ApiRequest, ApiEnvironment, HttpMethod } from '../../../types';
import { TabPanel } from './TabPanel';
import { ParamsTab } from './ParamsTab';
import { HeadersTab } from './HeadersTab';
import { BodyTab } from './BodyTab';
import { AuthTab } from './AuthTab';

const useStyles = makeStyles(theme => ({
  urlBar: {
    display: 'flex',
    marginBottom: theme.spacing(2),
    alignItems: 'flex-start',
  },
  methodSelect: {
    minWidth: 120,
    marginRight: theme.spacing(1),
  },
  urlField: {
    flexGrow: 1,
    marginRight: theme.spacing(1),
  },
  tabContent: {
    padding: theme.spacing(2),
    minHeight: '200px',
  },
  saveButton: {
    marginTop: theme.spacing(2),
  },
}));

interface RequestPanelProps {
  currentRequest: ApiRequest;
  onRequestChange: (request: ApiRequest) => void;
  onSendRequest: () => void;
  isLoading: boolean;
  selectedEnvironment?: ApiEnvironment;
}

export const RequestPanel: React.FC<RequestPanelProps> = ({
  currentRequest,
  onRequestChange,
  onSendRequest,
  isLoading,
  selectedEnvironment,
}) => {
  const classes = useStyles();
  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (_event: React.ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
  };

  const handleMethodChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    onRequestChange({
      ...currentRequest,
      method: event.target.value as HttpMethod,
    });
  };

  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onRequestChange({
      ...currentRequest,
      url: event.target.value,
    });
  };

  const handleParamsChange = (params: { key: string; value: string; enabled: boolean }[]) => {
    onRequestChange({
      ...currentRequest,
      params,
    });
  };

  const handleHeadersChange = (headers: { key: string; value: string; enabled: boolean }[]) => {
    onRequestChange({
      ...currentRequest,
      headers,
    });
  };

  const handleBodyChange = (body: ApiRequest['body']) => {
    onRequestChange({
      ...currentRequest,
      body,
    });
  };

  const handleAuthChange = (auth: ApiRequest['auth']) => {
    onRequestChange({
      ...currentRequest,
      auth,
    });
  };

  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onRequestChange({
      ...currentRequest,
      name: event.target.value,
    });
  };

  const getMethodColor = (method: HttpMethod): string => {
    switch (method) {
      case 'GET': return '#61affe';
      case 'POST': return '#49cc90';
      case 'PUT': return '#fca130';
      case 'DELETE': return '#f93e3e';
      case 'PATCH': return '#50e3c2';
      case 'HEAD': return '#9012fe';
      case 'OPTIONS': return '#0d5aa7';
      default: return '#6c757d';
    }
  };

  const getUrlHelperText = (url: string): string => {
    if (!url) {
      return "Enter a URL to send a request";
    }
    return "Request will be sent directly to the target URL";
  };

  return (
    <>
      <TextField
        label="Request Name"
        value={currentRequest.name}
        onChange={handleNameChange}
        fullWidth
        margin="normal"
        variant="outlined"
        size="small"
      />

      {/* Request URL bar */}
      <div className={classes.urlBar}>
        <FormControl variant="outlined" size="small" className={classes.methodSelect}>
          <Select
            value={currentRequest.method}
            onChange={handleMethodChange}
            renderValue={(value) => (
              <Typography
                component="span"
                style={{ color: getMethodColor(value as HttpMethod), fontWeight: 'bold' }}
              >
                {value as HttpMethod}
              </Typography>
            )}
          >
            {(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'] as HttpMethod[]).map((methodName) => (
              <MenuItem key={methodName} value={methodName}>
                <Typography component="span" style={{ color: getMethodColor(methodName), fontWeight: 'bold' }}>
                  {methodName}
                </Typography>
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <TextField
          className={classes.urlField}
          variant="outlined"
          size="small"
          placeholder="Enter request URL"
          value={currentRequest.url}
          onChange={handleUrlChange}
          helperText={getUrlHelperText(currentRequest.url)}
        />
        <Button
          variant="contained"
          color="primary"
          startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          onClick={onSendRequest}
          disabled={isLoading || !currentRequest.url}
        >
          Send
        </Button>
      </div>

      {/* Request tabs */}
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        indicatorColor="primary"
        textColor="primary"
      >
        <Tab label="Params" />
        <Tab label="Headers" />
        <Tab label="Body" />
        <Tab label="Auth" />
      </Tabs>

      {/* Params tab */}
      <TabPanel value={tabValue} index={0}>
        <ParamsTab
          params={currentRequest.params}
          onChange={handleParamsChange}
        />
      </TabPanel>

      {/* Headers tab */}
      <TabPanel value={tabValue} index={1}>
        <HeadersTab
          headers={currentRequest.headers}
          onChange={handleHeadersChange}
        />
      </TabPanel>

      {/* Body tab */}
      <TabPanel value={tabValue} index={2}>
        <BodyTab
          body={currentRequest.body}
          onChange={handleBodyChange}
        />
      </TabPanel>

      {/* Auth tab */}
      <TabPanel value={tabValue} index={3}>
        <AuthTab
          auth={currentRequest.auth}
          onChange={handleAuthChange}
        />
      </TabPanel>
    </>
  );
};
