import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  makeStyles,
} from '@material-ui/core';
import Paper from '@material-ui/core/Paper';
import { InfoCard } from '@backstage/core-components';
import { Alert } from '@material-ui/lab';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';

// Icons
import TestingIcon from '@material-ui/icons/BugReport';

// Custom hooks
import { useCollections } from './hooks/useCollections';
import { useRequest } from './hooks/useRequest';
import { useEnvironments } from './hooks/useEnvironments';

// Components
import { CollectionsSidebar } from './components/CollectionsSidebar';
import { RequestPanel } from './components/RequestPanel';
import { ResponsePanel } from './components/ResponsePanel';
import { EnvironmentSelector } from './components/EnvironmentSelector';
import { ImportDialog } from './ImportDialog';
import { ExportDialog } from './ExportDialog';
import { EnvironmentDialog } from './EnvironmentDialog';
import { CreateRequestDialog } from './CreateRequestDialog';
import { CreateFolderDialog } from './CreateFolderDialog';

// Types
import { ApiRequest } from '../../types';

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(2),
  },
  infoCard: {
    marginBottom: theme.spacing(2),
  },
  mainArea: {
    display: 'flex',
    flexDirection: 'column',
  },
  paper: {
    padding: theme.spacing(2),
  },
  divider: {
    marginBottom: theme.spacing(2),
  },
  urlBar: {
    display: 'flex',
    marginBottom: theme.spacing(2),
    alignItems: 'flex-start',
  },
  methodSelect: {
    minWidth: 120,
    marginRight: theme.spacing(1),
  },
  urlField: {
    flexGrow: 1,
    marginRight: theme.spacing(1),
  },
  responsePanel: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
  },
}));

export const ApiTestingPageRefactored = () => {
  const classes = useStyles();
  const errorApi = useApi(errorApiRef);

  // Use custom hooks
  const {
    collections,
    setCollections,
    collectionsLoading,
    collectionsError,
    expandedFolders,
    selectedItemId,
    setSelectedItemId,
    handleFolderToggle,
    handleItemSelect,
    handleAddCollection,
    handleDeleteCollection,
    handleImportCollection,
  } = useCollections();

  const {
    currentRequest,
    setCurrentRequest,
    currentResponse,
    setCurrentResponse,
    isLoading,
    handleSendRequest,
  } = useRequest(collections, setCollections);

  const {
    environments,
    setEnvironments,
    currentEnvironment,
    setCurrentEnvironment,
    isEnvironmentDialogOpen,
    setIsEnvironmentDialogOpen,
    isImportEnvironmentDialogOpen,
    setIsImportEnvironmentDialogOpen,
    environmentToEdit,
    setEnvironmentToEdit,
    handleAddEnvironment,
    handleEditEnvironment,
    handleSaveEnvironment,
    handleDeleteEnvironment,
    handleImportEnvironment,
  } = useEnvironments();

  // State for snackbar
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // State for dialogs
  const [isImportDialogOpen, setIsImportDialogOpen] = useState<boolean>(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState<boolean>(false);
  const [isCreateRequestDialogOpen, setIsCreateRequestDialogOpen] = useState<boolean>(false);
  const [isCreateFolderDialogOpen, setIsCreateFolderDialogOpen] = useState<boolean>(false);
  const [isDeleteConfirmDialogOpen, setIsDeleteConfirmDialogOpen] = useState<boolean>(false);
  const [itemToDelete, setItemToDelete] = useState<{ id: string; type: 'collection' | 'request' } | null>(null);
  const [selectedCollectionId, setSelectedCollectionId] = useState<string>('');
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({
      ...snackbar,
      open: false,
    });
  };

  const handleUpdateRequest = (updatedRequest: ApiRequest) => {
    setCurrentRequest(updatedRequest); // from useRequest

    // Also update this request in the main collections state
    if (selectedItemId && updatedRequest.id === selectedItemId) {
      const collectionIndex = collections.findIndex(c => c.requests[selectedItemId]);
      if (collectionIndex !== -1) {
        const newCollections = [...collections];
        const collectionToUpdate = { ...newCollections[collectionIndex] };
        collectionToUpdate.requests = {
          ...collectionToUpdate.requests,
          [selectedItemId]: updatedRequest,
        };
        newCollections[collectionIndex] = collectionToUpdate;
        setCollections(newCollections); // from useCollections

        // TODO: Add a visual indicator that changes are unsaved to the collection
        // TODO: Add a "Save Collection" button or similar mechanism to persist.
        // For now, this makes the UI behave more like Postman by reflecting changes immediately in the "session".
        // Consider adding a snackbar notification for unsaved changes or auto-save feature.
      }
    }
  };


  // Handle add folder
  const handleAddFolder = (collectionId: string) => {
    setSelectedCollectionId(collectionId);
    setIsCreateFolderDialogOpen(true);
  };

  // Handle create folder
  const handleCreateFolder = async (folderName: string, parentId: string | null) => {
    try {
      // Find the collection
      const collection = collections.find(c => c.id === selectedCollectionId);
      if (!collection) return;

      // Create a new folder
      const folderId = `folder_${Date.now()}`;
      const newFolder = {
        id: folderId,
        name: folderName,
        description: '',
        folders: [],
        requests: [],
      };

      // Update the collection
      const updatedCollections = collections.map(c => {
        if (c.id === selectedCollectionId) {
          if (parentId) {
            // Add to parent folder
            const addToFolder = (folders: any[]) => {
              return folders.map(folder => {
                if (folder.id === parentId) {
                  return {
                    ...folder,
                    folders: [...folder.folders, newFolder],
                  };
                } else if (folder.folders.length > 0) {
                  return {
                    ...folder,
                    folders: addToFolder(folder.folders),
                  };
                }
                return folder;
              });
            };

            return {
              ...c,
              folders: addToFolder(c.folders),
            };
          } else {
            // Add to root
            return {
              ...c,
              folders: [...c.folders, newFolder],
            };
          }
        }
        return c;
      });

      setCollections(updatedCollections);
      setSnackbar({
        open: true,
        message: 'Folder created successfully',
        severity: 'success',
      });
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      setSnackbar({
        open: true,
        message: 'Failed to create folder',
        severity: 'error',
      });
    }
  };

  // Handle add request
  const handleAddRequest = (collectionId: string, folderId?: string) => {
    setSelectedCollectionId(collectionId);
    setSelectedFolderId(folderId || null);
    setIsCreateRequestDialogOpen(true);
  };

  // Handle create request
  const handleCreateRequest = async (
    requestName: string,
    method: any,
    url: string,
    parentId: string | null,
    collectionId: string
  ) => {
    try {
      // Find the collection
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) return;

      // Create a new request
      const requestId = `req_${Date.now()}`;
      const newRequest = {
        id: requestId,
        name: requestName,
        method,
        url,
        headers: [],
        params: [],
        body: {
          mode: 'none',
          enabled: true,
        },
        preRequestScript: '',
        testScript: '',
      };

      // Update the collection
      const updatedCollections = collections.map(c => {
        if (c.id === collectionId) {
          // Add request to the collection's requests map
          const updatedRequests = {
            ...c.requests,
            [requestId]: newRequest,
          };

          if (parentId) {
            // Add to parent folder
            const addToFolder = (folders: any[]) => {
              return folders.map(folder => {
                if (folder.id === parentId) {
                  return {
                    ...folder,
                    requests: [...folder.requests, requestId],
                  };
                } else if (folder.folders.length > 0) {
                  return {
                    ...folder,
                    folders: addToFolder(folder.folders),
                  };
                }
                return folder;
              });
            };

            return {
              ...c,
              requests: updatedRequests,
              folders: addToFolder(c.folders),
            };
          } else {
            // Add to root (no folder)
            return {
              ...c,
              requests: updatedRequests,
            };
          }
        }
        return c;
      });

      setCollections(updatedCollections);
      setSnackbar({
        open: true,
        message: 'Request created successfully',
        severity: 'success',
      });
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      setSnackbar({
        open: true,
        message: 'Failed to create request',
        severity: 'error',
      });
    }
  };

  // Handle delete request
  const handleDeleteRequest = (requestId: string) => {
    setItemToDelete({ id: requestId, type: 'request' });
    setIsDeleteConfirmDialogOpen(true);
  };

  // Handle duplicate request
  const handleDuplicateRequest = (requestId: string) => {
    // Find the collection that contains this request
    const collection = collections.find(c => c.requests[requestId]);
    if (!collection) return;

    const originalRequest = collection.requests[requestId];
    const newRequestId = `req_${Date.now()}`;

    // Create a copy of the request with a new ID
    const newRequest = {
      ...originalRequest,
      id: newRequestId,
      name: `${originalRequest.name} (Copy)`,
    };

    // Find the folder that contains the request
    const findFolderWithRequest = (folders: any[], reqId: string): string | undefined => {
      for (const folder of folders) {
        if (folder.requests.includes(reqId)) {
          return folder.id;
        }

        if (folder.folders.length > 0) {
          const nestedResult = findFolderWithRequest(folder.folders, reqId);
          if (nestedResult) {
            return nestedResult;
          }
        }
      }

      return undefined;
    };

    const targetFolderId = findFolderWithRequest(collection.folders, requestId);

    // Update the collection
    const updatedCollections = collections.map(c => {
      if (c.id === collection.id) {
        // Add the new request to the requests map
        const updatedRequests = {
          ...c.requests,
          [newRequestId]: newRequest,
        };

        if (targetFolderId) {
          // Add to the same folder as the original
          const addToFolder = (folders: any[]) => {
            return folders.map(folder => {
              if (folder.id === targetFolderId) {
                return {
                  ...folder,
                  requests: [...folder.requests, newRequestId],
                };
              } else if (folder.folders.length > 0) {
                return {
                  ...folder,
                  folders: addToFolder(folder.folders),
                };
              }
              return folder;
            });
          };

          return {
            ...c,
            requests: updatedRequests,
            folders: addToFolder(c.folders),
          };
        } else {
          // The original request was at the root level
          return {
            ...c,
            requests: updatedRequests,
          };
        }
      }
      return c;
    });

    setCollections(updatedCollections);
    setSnackbar({
      open: true,
      message: 'Request duplicated successfully',
      severity: 'success',
    });
  };

  // Handle confirm delete
  const handleConfirmDelete = async () => {
    if (!itemToDelete) return;

    try {
      if (itemToDelete.type === 'collection') {
        await handleDeleteCollection(itemToDelete.id);
      } else if (itemToDelete.type === 'request') {
        // Find the collection that contains this request
        const collection = collections.find(c => c.requests[itemToDelete.id]);
        if (!collection) return;

        // Find the folder that contains the request
        const findFolderWithRequest = (folders: any[], reqId: string): { folder: any; path: any[] } | undefined => {
          for (const folder of folders) {
            if (folder.requests.includes(reqId)) {
              return { folder, path: [folder.id] };
            }

            if (folder.folders.length > 0) {
              const nestedResult = findFolderWithRequest(folder.folders, reqId);
              if (nestedResult) {
                return {
                  folder: nestedResult.folder,
                  path: [folder.id, ...nestedResult.path],
                };
              }
            }
          }

          return undefined;
        };

        const folderInfo = findFolderWithRequest(collection.folders, itemToDelete.id);

        // Update the collection
        const updatedCollections = collections.map(c => {
          if (c.id === collection.id) {
            // Create a new requests object without the deleted request
            const { [itemToDelete.id]: deletedRequest, ...remainingRequests } = c.requests;

            if (folderInfo) {
              // Remove from folder
              const removeFromFolder = (folders: any[], path: string[], index = 0): any[] => {
                return folders.map(folder => {
                  if (folder.id === path[index]) {
                    if (index === path.length - 1) {
                      // This is the target folder
                      return {
                        ...folder,
                        requests: folder.requests.filter((id: string) => id !== itemToDelete!.id),
                      };
                    } else {
                      // Continue down the path
                      return {
                        ...folder,
                        folders: removeFromFolder(folder.folders, path, index + 1),
                      };
                    }
                  }
                  return folder;
                });
              };

              return {
                ...c,
                requests: remainingRequests,
                folders: removeFromFolder(c.folders, folderInfo.path),
              };
            } else {
              // The request was at the root level
              return {
                ...c,
                requests: remainingRequests,
              };
            }
          }
          return c;
        });

        setCollections(updatedCollections);

        // If this was the current request, clear it
        if (currentRequest.id === itemToDelete.id) {
          setCurrentRequest({
            id: `req_${Date.now()}`,
            name: 'New Request',
            method: 'GET',
            url: '',
            headers: [],
            params: [],
            body: {
              mode: 'none',
              enabled: true,
            },
            preRequestScript: '',
            testScript: '',
          });
          setCurrentResponse(null);
        }
      }

      setSnackbar({
        open: true,
        message: `${itemToDelete.type === 'collection' ? 'Collection' : 'Request'} deleted successfully`,
        severity: 'success',
      });
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      setSnackbar({
        open: true,
        message: `Failed to delete ${itemToDelete.type}`,
        severity: 'error',
      });
    } finally {
      setIsDeleteConfirmDialogOpen(false);
      setItemToDelete(null);
    }
  };

  // Handle edit collection
  const handleEditCollection = (collectionId: string) => {
    // Implementation for editing collection
    console.log('Edit collection', collectionId);
  };

  // Handle send request with current environment
  const handleSendWithEnvironment = async () => {
    const currentEnv = environments.find(env => env.id === currentEnvironment);
    const result = await handleSendRequest(currentEnv);

    if (result.success) {
      setSnackbar({
        open: true,
        message: 'Request sent successfully',
        severity: 'success',
      });
    } else {
      setSnackbar({
        open: true,
        message: `Request failed: ${result.error}`,
        severity: 'error',
      });
    }
  };

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <InfoCard
            title={
              <Box display="flex" alignItems="center">
                <TestingIcon style={{ marginRight: '8px' }} />
                API Testing
              </Box>
            }
            className={classes.infoCard}
          >
            <Typography variant="body1" paragraph>
              Test your APIs by sending requests and viewing responses. Organize your requests into collections and use environments to manage variables.
            </Typography>
          </InfoCard>
        </Grid>

        {/* Main content grid */}
        <Grid container item xs={12} spacing={3}>
          {/* Collections sidebar */}
          <Grid item xs={12} md={3}>
            <CollectionsSidebar
              collections={collections}
              collectionsLoading={collectionsLoading}
              collectionsError={collectionsError}
              expandedFolders={expandedFolders}
              selectedItemId={selectedItemId}
              onFolderToggle={handleFolderToggle}
              onItemSelect={handleItemSelect}
              onAddCollection={handleAddCollection}
              onDeleteCollection={(id) => {
                setItemToDelete({ id, type: 'collection' });
                setIsDeleteConfirmDialogOpen(true);
              }}
              onEditCollection={handleEditCollection}
              onAddFolder={handleAddFolder}
              onAddRequest={handleAddRequest}
              onDeleteRequest={handleDeleteRequest}
              onDuplicateRequest={handleDuplicateRequest}
            />
          </Grid>

          {/* Request panel */}
          <Grid item xs={12} md={9}>
            {/* Environment selector */}
            <EnvironmentSelector
              environments={environments}
              currentEnvironment={currentEnvironment}
              onEnvironmentChange={setCurrentEnvironment}
              onAddEnvironment={handleAddEnvironment}
              onEditEnvironment={handleEditEnvironment}
              onImportEnvironment={() => setIsImportEnvironmentDialogOpen(true)}
            />

            {/* Request panel */}
            <RequestPanel
              currentRequest={currentRequest}
              onRequestChange={handleUpdateRequest}
              onSendRequest={handleSendWithEnvironment}
              isLoading={isLoading}
              selectedEnvironment={environments.find(env => env.id === currentEnvironment)}
            />

            {/* Response panel */}
            <Paper className={classes.responsePanel} elevation={1}>
              <ResponsePanel
                response={currentResponse}
                isLoading={isLoading}
              />
            </Paper>
          </Grid>
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Import dialog */}
      <ImportDialog
        open={isImportDialogOpen}
        onClose={() => setIsImportDialogOpen(false)}
        onImport={handleImportCollection}
      />

      {/* Export dialog */}
      <ExportDialog
        open={isExportDialogOpen}
        onClose={() => setIsExportDialogOpen(false)}
        collections={collections}
      />

      {/* Environment dialog */}
      <EnvironmentDialog
        open={isEnvironmentDialogOpen}
        onClose={() => {
          setIsEnvironmentDialogOpen(false);
          setEnvironmentToEdit(null);
        }}
        onSave={handleSaveEnvironment}
        environment={environmentToEdit}
      />

      {/* Import environment dialog */}
      <ImportDialog
        open={isImportEnvironmentDialogOpen}
        onClose={() => setIsImportEnvironmentDialogOpen(false)}
        onImport={handleImportEnvironment}
        importType="environment"
      />

      {/* Create request dialog */}
      <CreateRequestDialog
        open={isCreateRequestDialogOpen}
        onClose={() => setIsCreateRequestDialogOpen(false)}
        onCreateRequest={handleCreateRequest}
        collections={collections}
        selectedCollectionId={selectedCollectionId}
        selectedFolderId={selectedFolderId}
      />

      {/* Create folder dialog */}
      <CreateFolderDialog
        open={isCreateFolderDialogOpen}
        onClose={() => setIsCreateFolderDialogOpen(false)}
        onCreateFolder={handleCreateFolder}
        collectionId={selectedCollectionId}
      />

      {/* Delete confirmation dialog */}
      <Dialog
        open={isDeleteConfirmDialogOpen}
        onClose={() => setIsDeleteConfirmDialogOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this {itemToDelete?.type}? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsDeleteConfirmDialogOpen(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleConfirmDelete} color="secondary">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};
